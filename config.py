"""
Configuration file for OnlyMonster automation
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# OnlyMonster credentials
EMAIL = "<EMAIL>"
PASSWORD = "Shutup12!*"

# URLs
LOGIN_URL = "https://onlymonster.ai/auth/signin"
TRACKING_LINKS_URL = "https://onlymonster.ai/panel/tracking-links"

# Database configuration
DATABASE_PATH = "onlymonster_data.db"

# Selenium configuration
HEADLESS = False  # Set to True to run browser in background
IMPLICIT_WAIT = 10  # seconds
PAGE_LOAD_TIMEOUT = 30  # seconds

# OpenRouter API configuration
OPENROUTER_API_KEY = "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"

# Priority tracking links for focused analysis
PRIORITY_LINKS = [
    "reels-naominoface",
    "reddit-babycheeksx",
    "reels-lilfoxnaomi-aug-22",
    "chive-nyla-aug-8",
    "tiktok-aug-1-24",
    "chive-aug-1-24",
    "Reels2024"
]

# Slack configuration
SLACK_BOT_TOKEN = "xoxe.xoxp-1-Mi0yLTkwMTU1MDUzMjI3MDktOTAxNTUwNTM0MDkxNy05MDAyMDU4MDExNDk1LTkwMDIwNTgwMjA1MDMtZGRmOGUzNDU3YWE0ODRlYjk1YjA4NTA5ZTdiMDQ3ZTZhYjlmNDJmOWEwYWNmMDc4M2UzMjg2NTZlNjQyMWQ4YQ"
SLACK_REFRESH_TOKEN = "xoxe-1-My0xLTkwMTU1MDUzMjI3MDktOTAwMjA1ODAxMTQ5NS05MDAyMDU4MDU3NDE1LTkzNzk3YjAzMmM0MGI5NDI2NWZmNDA2MDY2ZGU4OTMxYzJlNjIxNGY2YWI0MmIzZGQ3ZDhkODRlNTlkYzZlM2U"
SLACK_APP_TOKEN = "xapp-1-A0917GCNQ1W-9010643051267-734bd5c0a0c3939c868b44df82f84249e4983d055d711c7cf10079d46f72cefc"
SLACK_CHANNEL_ID = "C090FEVA18D"  # Extracted from your channel URL

# Slack app credentials
SLACK_APP_ID = "A0917GCNQ1W"
SLACK_CLIENT_ID = "9015505322709.9041556772064"
SLACK_CLIENT_SECRET = "185f6724cc4efcebc25d2442c76363c4"
SLACK_SIGNING_SECRET = "1fa923b12c32bfcc3e113c1856b42102"
SLACK_VERIFICATION_TOKEN = "tBasALwVIzj8HiFfr4U7XuM9"
