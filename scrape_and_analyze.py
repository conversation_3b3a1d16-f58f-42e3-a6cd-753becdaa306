#!/usr/bin/env python3
"""
Run a fresh scrape and immediate analysis
"""
from onlymonster_scraper import OnlyMonsterScraper
from analytics import TrackingAnalytics

def main():
    print("🚀 Starting OnlyMonster Scrape & Analysis")
    print("="*60)
    
    # Run scraper
    scraper = OnlyMonsterScraper()
    try:
        data = scraper.run_scraping()
        
        if data:
            print(f"\n✅ Successfully scraped {len(data)} tracking links")
            
            # Run analytics
            print("\n🔍 Running Analytics...")
            analytics = TrackingAnalytics()
            analysis = analytics.run_analysis(24)
            analytics.print_analysis_report(analysis)
        else:
            print("❌ No data collected")
            
    except Exception as e:
        print(f"❌ Scraping failed: {e}")

if __name__ == "__main__":
    main()
