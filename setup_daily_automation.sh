#!/bin/bash

# OnlyMonster Daily Report Automation Setup Script
# This script sets up automatic daily execution of the OnlyMonster report

echo "🚀 Setting up OnlyMonster Daily Report Automation"
echo "=================================================="

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📁 Project directory: $SCRIPT_DIR"

# Make the scheduler executable
chmod +x "$SCRIPT_DIR/daily_scheduler.py"
echo "✅ Made daily_scheduler.py executable"

# Option 1: Systemd Service (Recommended for servers)
echo ""
echo "🔧 Setting up systemd service..."

# Copy service file to systemd directory
sudo cp "$SCRIPT_DIR/onlymonster-daily.service" /etc/systemd/system/
echo "✅ Copied service file to /etc/systemd/system/"

# Reload systemd and enable the service
sudo systemctl daemon-reload
sudo systemctl enable onlymonster-daily.service
echo "✅ Enabled onlymonster-daily service"

# Start the service
sudo systemctl start onlymonster-daily.service
echo "✅ Started onlymonster-daily service"

# Check service status
echo ""
echo "📊 Service Status:"
sudo systemctl status onlymonster-daily.service --no-pager -l

echo ""
echo "🎯 AUTOMATION SETUP COMPLETE!"
echo "================================"
echo ""
echo "📅 Your OnlyMonster daily reports will now run automatically at 9:00 AM every day"
echo ""
echo "🔧 Management Commands:"
echo "  • Check status:    sudo systemctl status onlymonster-daily"
echo "  • View logs:       sudo journalctl -u onlymonster-daily -f"
echo "  • Stop service:    sudo systemctl stop onlymonster-daily"
echo "  • Start service:   sudo systemctl start onlymonster-daily"
echo "  • Restart service: sudo systemctl restart onlymonster-daily"
echo "  • Disable service: sudo systemctl disable onlymonster-daily"
echo ""
echo "📱 Reports will be sent to your Slack channel automatically"
echo "🕘 Next report scheduled for: Tomorrow at 9:00 AM"
echo ""
echo "✅ Setup complete! The automation is now running in the background."

# Option 2: Cron Job (Alternative method)
echo ""
echo "📋 Alternative: Cron Job Setup"
echo "=============================="
echo "If you prefer using cron instead of systemd, run:"
echo ""
echo "crontab -e"
echo ""
echo "Then add this line:"
echo "0 9 * * * cd $SCRIPT_DIR && /usr/bin/python3 daily_scheduler.py >> $SCRIPT_DIR/cron.log 2>&1"
echo ""

# Test run option
echo "🧪 Test the automation now? (y/n)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "🚀 Running test report..."
    cd "$SCRIPT_DIR"
    python3 daily_scheduler.py &
    SCHEDULER_PID=$!
    echo "✅ Test scheduler started (PID: $SCHEDULER_PID)"
    echo "⏱️  It will run the report immediately, then wait for the next scheduled time"
    echo "🛑 Press Ctrl+C to stop the test, or let it run in the background"
fi
