"""
OnlyMonster.ai Tracking Links Scraper
Automates login and data extraction from tracking links page
"""
import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from database import TrackingDatabase
from config import EMAIL, PASSWORD, LOGIN_URL, TRACKING_LINKS_URL, HEADLESS, IMPLICIT_WAIT, PAGE_LOAD_TIMEOUT


class OnlyMonsterScraper:
    def __init__(self):
        self.driver = None
        self.db = TrackingDatabase()
        self.setup_driver()
    
    def setup_driver(self):
        """Initialize Chrome WebDriver with appropriate options"""
        chrome_options = Options()

        # Always run headless in server environment
        chrome_options.add_argument("--headless=new")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        try:
            # Try to use system chromedriver first
            service = Service("/usr/bin/chromedriver")
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e1:
            print(f"System chromedriver failed: {e1}")
            # Fallback to ChromeDriverManager
            try:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e2:
                print(f"ChromeDriverManager also failed: {e2}")
                raise Exception(f"Failed to initialize Chrome WebDriver. System: {e1}, Manager: {e2}")

        self.driver.implicitly_wait(IMPLICIT_WAIT)
        self.driver.set_page_load_timeout(PAGE_LOAD_TIMEOUT)
    
    def login(self):
        """Login to OnlyMonster.ai"""
        print("Navigating to OnlyMonster.ai...")
        self.driver.get(LOGIN_URL)
        
        try:
            # Wait for and find email input
            email_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='email'], input[name='email'], input[placeholder*='email' i]"))
            )
            email_input.clear()
            email_input.send_keys(EMAIL)
            print("Email entered successfully")
            
            # Find password input
            password_input = self.driver.find_element(By.CSS_SELECTOR, "input[type='password'], input[name='password']")
            password_input.clear()
            password_input.send_keys(PASSWORD)
            print("Password entered successfully")
            
            # Find and click login button
            login_button = self.driver.find_element(By.CSS_SELECTOR, 
                "button[type='submit'], input[type='submit'], button:contains('Login'), button:contains('Sign in')")
            login_button.click()
            print("Login button clicked")
            
            # Wait for successful login (check for dashboard or redirect)
            WebDriverWait(self.driver, 15).until(
                lambda driver: "panel" in driver.current_url.lower() or "dashboard" in driver.current_url.lower()
            )
            print("Login successful!")
            
        except TimeoutException:
            print("Login failed - timeout waiting for elements")
            raise
        except NoSuchElementException as e:
            print(f"Login failed - element not found: {e}")
            raise
    
    def navigate_to_tracking_links(self):
        """Navigate to the tracking links page"""
        print("Navigating to tracking links page...")
        self.driver.get(TRACKING_LINKS_URL)
        
        # Wait for page to load
        WebDriverWait(self.driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        time.sleep(3)  # Additional wait for dynamic content
        print("Tracking links page loaded")
    
    def extract_tracking_data(self):
        """Extract tracking links data from the page"""
        print("Extracting tracking data...")
        tracking_data = []
        
        try:
            # Common selectors for tables and data rows
            possible_selectors = [
                "table tbody tr",
                ".tracking-link",
                ".link-item",
                "[data-tracking]",
                ".table-row",
                "tr:has(td)"
            ]
            
            rows = None
            for selector in possible_selectors:
                try:
                    rows = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if rows:
                        print(f"Found {len(rows)} rows using selector: {selector}")
                        break
                except:
                    continue
            
            if not rows:
                # Fallback: look for any elements containing numbers that might be clicks/fans
                print("Trying fallback method to find data...")
                page_source = self.driver.page_source
                print("Page source length:", len(page_source))
                
                # Save page source for debugging
                with open("page_source.html", "w", encoding="utf-8") as f:
                    f.write(page_source)
                print("Page source saved to page_source.html for debugging")
                
                return tracking_data
            
            for row in rows:
                try:
                    # Extract text from the row
                    row_text = row.text.strip()
                    if not row_text:
                        continue
                    
                    # Look for patterns that might contain tracking link name, clicks, and fans
                    # This is a flexible approach that can be adjusted based on actual page structure
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if not cells:
                        cells = row.find_elements(By.CSS_SELECTOR, "div, span")
                    
                    if len(cells) >= 3:
                        # Assume first cell is name, and look for numbers in other cells
                        name = cells[0].text.strip()
                        
                        clicks = 0
                        fans = 0
                        
                        for cell in cells[1:]:
                            cell_text = cell.text.strip()
                            numbers = re.findall(r'\d+', cell_text)
                            if numbers:
                                num = int(numbers[0])
                                if clicks == 0:
                                    clicks = num
                                elif fans == 0:
                                    fans = num
                                    break
                        
                        if name and (clicks > 0 or fans > 0):
                            tracking_data.append((name, clicks, fans))
                            print(f"Extracted: {name} - Clicks: {clicks}, Fans: {fans}")
                
                except Exception as e:
                    print(f"Error processing row: {e}")
                    continue
            
            print(f"Successfully extracted {len(tracking_data)} tracking links")
            return tracking_data
            
        except Exception as e:
            print(f"Error extracting tracking data: {e}")
            return tracking_data
    
    def run_scraping(self):
        """Main method to run the complete scraping process"""
        try:
            print("Starting OnlyMonster scraping...")
            
            # Login
            self.login()
            
            # Navigate to tracking links
            self.navigate_to_tracking_links()
            
            # Extract data
            tracking_data = self.extract_tracking_data()
            
            if tracking_data:
                # Store in database
                self.db.insert_tracking_data(tracking_data)
                print(f"Successfully stored {len(tracking_data)} records in database")
            else:
                print("No tracking data found to store")
            
            return tracking_data
            
        except Exception as e:
            print(f"Scraping failed: {e}")
            raise
        finally:
            if self.driver:
                self.driver.quit()
                print("Browser closed")


if __name__ == "__main__":
    scraper = OnlyMonsterScraper()
    try:
        data = scraper.run_scraping()
        print("Scraping completed successfully!")
        
        # Display recent data
        recent_data = scraper.db.get_latest_data(5)
        if recent_data:
            print("\nRecent tracking data:")
            for name, clicks, fans, timestamp in recent_data:
                print(f"  {name}: {clicks} clicks, {fans} fans ({timestamp})")
    except Exception as e:
        print(f"Scraping failed: {e}")
