[Unit]
Description=OnlyMonster Daily Report
After=network.target
Wants=network-online.target

[Service]
Type=oneshot
User=root
WorkingDirectory=/root/onlymonster-automations
ExecStart=/bin/bash -c 'source /root/onlymonster-automations/venv/bin/activate && python3 /root/onlymonster-automations/standardized_daily_report.py'
StandardOutput=journal
StandardError=journal

# Environment variables
Environment=PYTHONPATH=/root/onlymonster-automations
Environment=DISPLAY=:0

# Logging
SyslogIdentifier=onlymonster-daily
