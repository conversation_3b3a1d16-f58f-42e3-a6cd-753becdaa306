[Unit]
Description=OnlyMonster Daily Report Scheduler
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=root
WorkingDirectory=/root/onlymonster-automations
ExecStart=/usr/bin/python3 /root/onlymonster-automations/daily_scheduler.py
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

# Environment variables
Environment=PYTHONPATH=/root/onlymonster-automations
Environment=DISPLAY=:0

# Logging
SyslogIdentifier=onlymonster-daily

[Install]
WantedBy=multi-user.target
