"""
Database operations for OnlyMonster tracking data
"""
import sqlite3
import datetime
from typing import List, <PERSON><PERSON>
from config import DATABASE_PATH


class TrackingDatabase:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.init_database()
    
    def init_database(self):
        """Initialize the database and create tables if they don't exist"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS tracking_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tracking_link_name TEXT NOT NULL,
                    clicks INTEGER NOT NULL,
                    fans INTEGER NOT NULL,
                    timestamp DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()
    
    def insert_tracking_data(self, data: List[Tuple[str, int, int]]):
        """
        Insert tracking data into the database
        
        Args:
            data: List of tuples containing (tracking_link_name, clicks, fans)
        """
        timestamp = datetime.datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Prepare data with timestamp
            data_with_timestamp = [
                (name, clicks, fans, timestamp) 
                for name, clicks, fans in data
            ]
            
            cursor.executemany('''
                INSERT INTO tracking_data (tracking_link_name, clicks, fans, timestamp)
                VALUES (?, ?, ?, ?)
            ''', data_with_timestamp)
            
            conn.commit()
            print(f"Inserted {len(data)} records at {timestamp}")
    
    def get_latest_data(self, limit: int = 10):
        """Get the most recent tracking data"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, timestamp
                FROM tracking_data
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (limit,))
            return cursor.fetchall()
    
    def get_data_by_date(self, date: str):
        """Get tracking data for a specific date (YYYY-MM-DD format)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT tracking_link_name, clicks, fans, timestamp
                FROM tracking_data
                WHERE DATE(timestamp) = ?
                ORDER BY timestamp DESC
            ''', (date,))
            return cursor.fetchall()
