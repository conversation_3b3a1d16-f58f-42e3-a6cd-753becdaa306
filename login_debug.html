<html lang="en"><head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In | OnlyMonster</title>

    <link rel="stylesheet" href="https://cdn.onlymonster.ai/public/assets/css/bootstrap.min.css?**********">
    <link rel="stylesheet" href="https://cdn.onlymonster.ai/public/assets/css/lineicons.css?**********">
    <link rel="stylesheet" href="https://cdn.onlymonster.ai/public/assets/css/main.css?**********">
    <link rel="icon" type="image/svg+xml" href="https://cdn.onlymonster.ai/public/assets/images/favicon.svg?**********">
    <style>
        .profile-box .dropdown-toggle::after, .profile-box .lni, .profile-box .info .d-flex, .profile-box .settings {
            display: none!important;
        }
    </style>
        <meta name="description" content="Sign in to your OnlyMonster.ai account to access exclusive features and connect with the monster community. Secure and easy login process.">
    <style>
        .signup-wrapper {
            padding: 0!important;
            background: transparent!important;
        }
        .cl-card {
            background: #13120F!important;
            border: 1px solid #2A2820;
        }
        .cl-headerSubtitle, .cl-formFieldLabel, .cl-footerActionText {
            color: white!important;
        }
        .cl-formFieldInput {
            border: 1px solid #2A2820!important;
            background: #1A1813!important;
        }
        .cl-logoBox, .cl-headerSubtitle {
            display: none;
        }
        .darkTheme .signup-wrapper, .darkTheme .auth-wrapper {
            background: transparent;
            border-color: transparent;
        }
    </style>
</head>
<body class="darkTheme">
<section class="signin-section">
    <div>
        <div class="row g-0 auth-row min-vh-100 auth-cover-wrapper align-items-center">
            <div class="col-lg-4 mt-40 mb-40">
                <div class="text-center">
                    <img class="mb-3" width="193" height="58" src="https://cdn.onlymonster.ai/public/assets/images/logo-colored.svg?**********">
                </div>
                                    <div class="signup-wrapper notranslate" style="border-radius: 12px;" translate="no">
        <div class="form-wrapper">
            <div class="d-flex justify-content-center">
                <div id="sign-in-block"></div>
            </div>
        </div>
    </div>

    <script src="https://connect.facebook.net/signals/config/612406711439005?v=2.9.205&amp;r=stable&amp;domain=onlymonster.ai&amp;hme=91e1bfdb39aff53ea3a4a1f39ed64da8fd6f63253e5c24269dbb2994c8fed743&amp;ex_m=74%2C128%2C113%2C117%2C65%2C6%2C106%2C73%2C19%2C101%2C93%2C55%2C58%2C11%2C184%2C205%2C212%2C208%2C209%2C211%2C32%2C107%2C57%2C81%2C210%2C179%2C182%2C51%2C50%2C206%2C207%2C192%2C140%2C45%2C197%2C194%2C195%2C37%2C152%2C18%2C54%2C201%2C200%2C142%2C21%2C44%2C2%2C47%2C69%2C70%2C71%2C75%2C97%2C20%2C17%2C100%2C96%2C95%2C114%2C56%2C116%2C42%2C115%2C33%2C98%2C43%2C90%2C29%2C180%2C183%2C149%2C14%2C15%2C16%2C8%2C9%2C28%2C25%2C26%2C61%2C66%2C68%2C79%2C105%2C108%2C30%2C80%2C12%2C10%2C84%2C52%2C24%2C110%2C109%2C111%2C102%2C13%2C23%2C4%2C41%2C78%2C22%2C162%2C136%2C77%2C1%2C99%2C60%2C88%2C36%2C31%2C86%2C87%2C92%2C40%2C7%2C94%2C85%2C48%2C35%2C38%2C0%2C72%2C118%2C91%2C5%2C89%2C249%2C177%2C126%2C165%2C158%2C3%2C39%2C67%2C46%2C112%2C49%2C83%2C64%2C63%2C34%2C103%2C62%2C59%2C53%2C82%2C76%2C27%2C104%2C119" async=""></script><script async="" src="https://connect.facebook.net/en_US/fbevents.js"></script><script type="text/javascript" async="" src="https://widget.intercom.io/widget/ksh6br93"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=G-FFFCEGYFP6&amp;cx=c&amp;gtm=45He5641v9119501664za200&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********"></script><script type="text/javascript" async="" src="https://www.googletagmanager.com/gtag/js?id=AW-***********&amp;cx=c&amp;gtm=45He5641v9119501664za200&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-5TCW4TD"></script><script>
        const script = document.createElement('script');
        script.setAttribute('data-clerk-publishable-key', `pk_live_Y2xlcmsub25seW1vbnN0ZXIuYWkk`);
        script.async = true;
        script.src = `https://accounts.onlymonster.ai/npm/@clerk/clerk-js@latest/dist/clerk.browser.js`;

        script.addEventListener('load', async function () {
            await window.Clerk.load();
            window.Clerk.mountSignIn(document.querySelector('#sign-in-block'), {
                redirectUrl: 'https://onlymonster.ai/panel',
                afterSignInUrl: 'https://onlymonster.ai/panel',
                forceRedirectUrl: 'https://onlymonster.ai/panel',
                signUpUrl: '/auth/signup',
                appearance: {
                    baseTheme: {
                        variables: {
                            colorBackground: '#212126',
                            colorNeutral: 'white',
                            colorPrimary: '#0A6DFF',
                            colorTextOnPrimaryBackground: 'white',
                            colorText: 'white',
                            colorInputText: 'white',
                            colorInputBackground: '#2A2820',
                        },
                        elements: {
                            providerIcon__apple: { filter: 'invert(1)' },
                            providerIcon__github: { filter: 'invert(1)' },
                            activeDeviceIcon: {
                                '--cl-chassis-bottom': '#d2d2d2',
                                '--cl-chassis-back': '#e6e6e6',
                                '--cl-chassis-screen': '#e6e6e6',
                                '--cl-screen': '#111111',
                            },
                        },
                    }
                }
            });
        });
        document.body.appendChild(script);
    </script>
            <script type="text/javascript" async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/***********/?random=1749403095677&amp;cv=11&amp;fst=1749403095677&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;gtm=45be5641v9208249870z89119501664za200zb9119501664&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&amp;u_w=800&amp;u_h=600&amp;url=https%3A%2F%2Fonlymonster.ai%2Fauth%2Fsignin&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Sign%20In%20%7C%20OnlyMonster&amp;npa=0&amp;pscdl=noapi&amp;auid=*********.**********&amp;uaa=&amp;uab=&amp;uafvl=&amp;uamb=0&amp;uam=&amp;uap=Linux&amp;uapv=&amp;uaw=0&amp;fledge=1&amp;data=event%3Dgtag.config&amp;rfmt=3&amp;fmt=4"></script></div>
        </div>
    </div>
</section><script data-clerk-publishable-key="pk_live_Y2xlcmsub25seW1vbnN0ZXIuYWkk" async="" src="https://accounts.onlymonster.ai/npm/@clerk/clerk-js@latest/dist/clerk.browser.js"></script>
<script src="https://cdn.onlymonster.ai/public/assets/js/axios.min.js?**********"></script>
<script>
    axios.interceptors.request.use(
        async function (config) {
            if (!config.url.includes('http')) {
                const token = document.cookie
                    .split('; ')
                    .find(row => row.startsWith('__session='))
                    ?.split('=')[1];
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            return config;
        },
        function (error) {
            return Promise.reject(error);
        }
    );
</script>
<script src="https://cdn.onlymonster.ai/public/assets/js/bootstrap.bundle.min.js?**********"></script>
<script src="https://cdn.onlymonster.ai/public/assets/js/main.js?**********"></script>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5TCW4TD');</script>
<!-- End Google Tag Manager -->
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5TCW4TD"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
    <script src="https://js.sentry-cdn.com/3f2404227d857a3af10d6b8e827462a3.min.js" crossorigin="anonymous"></script>
<script>
    window.intercomSettings = {
        api_base: "https://api-iam.intercom.io",
        app_id: "ksh6br93",
            };
</script>

<script>
    if (window.navigator?.userAgent?.includes('OnlyMonsterBrowser') && location.href.includes('auth/')) {
        const style = document.createElement('style');
        style.innerHTML = `.intercom-app, .intercom-lightweight-app { display: none!important; }`;
        document.head.appendChild(style);
    }
</script>



<script type="text/javascript" id="" charset="">!function(b,e,f,g,a,c,d){b.fbq||(a=b.fbq=function(){a.callMethod?a.callMethod.apply(a,arguments):a.queue.push(arguments)},b._fbq||(b._fbq=a),a.push=a,a.loaded=!0,a.version="2.0",a.queue=[],c=e.createElement(f),c.async=!0,c.src=g,d=e.getElementsByTagName(f)[0],d.parentNode.insertBefore(c,d))}(window,document,"script","https://connect.facebook.net/en_US/fbevents.js");fbq("init","612406711439005");fbq("track","PageView");</script>
<noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=612406711439005&amp;ev=PageView&amp;noscript=1"></noscript><iframe height="0" width="0" style="display: none; visibility: hidden;"></iframe><iframe allow="join-ad-interest-group" data-tagging-id="AW-***********" data-load-time="1749403095705" height="0" width="0" src="https://td.doubleclick.net/td/rul/***********?random=1749403095677&amp;cv=11&amp;fst=1749403095677&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;gtm=45be5641v9208249870z89119501664za200zb9119501664&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********&amp;u_w=800&amp;u_h=600&amp;url=https%3A%2F%2Fonlymonster.ai%2Fauth%2Fsignin&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Sign%20In%20%7C%20OnlyMonster&amp;npa=0&amp;pscdl=noapi&amp;auid=*********.**********&amp;uaa=&amp;uab=&amp;uafvl=&amp;uamb=0&amp;uam=&amp;uap=Linux&amp;uapv=&amp;uaw=0&amp;fledge=1&amp;data=event%3Dgtag.config" style="display: none; visibility: hidden;"></iframe>

<iframe id="intercom-frame" style="position: absolute !important; opacity: 0 !important; width: 1px !important; height: 1px !important; top: 0 !important; left: 0 !important; border: none !important; display: block !important; z-index: -1 !important; pointer-events: none;" aria-hidden="true" tabindex="-1" title="Intercom"></iframe><div id="intercom-container" class="intercom-namespace"><style>html.intercom-mobile-messenger-active,html.intercom-mobile-messenger-active>body,html.intercom-modal-open,#intercom-container-body{overflow:hidden!important;}html.intercom-mobile-messenger-active,html.intercom-mobile-messenger-active>body{position:static!important;transform:none!important;}html.intercom-mobile-messenger-active>body{height:0!important;margin:0!important;}iframe#intercom-frame{position:absolute!important;opacity:0!important;width:1px!important;height:1px!important;top:0!important;left:0!important;border:none!important;display:block!important;z-index:-1!important;;};;</style><div class="intercom-app" aria-live="polite"><div class="intercom-messenger-frame intercom-with-namespace-n8q0v8 ed8ycz90"></div><div class="intercom-with-namespace-1vdrh3a edrs4yi0"><iframe allowfullscreen="" class="intercom-launcher-frame intercom-with-namespace-1d6mqa9 efr23pf0" name="intercom-launcher-frame" title="Intercom live chat" data-intercom-frame="true" role="dialog"></iframe></div><div id="intercom-modal-container"></div></div></div><div id="intercom-css-container"><style data-emotion="intercom-with-namespace-global" data-s=""></style><style data-emotion="intercom-with-namespace" data-s=""></style></div></body></html>